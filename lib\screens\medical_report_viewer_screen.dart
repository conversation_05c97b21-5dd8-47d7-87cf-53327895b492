import 'dart:io';
import 'package:flutter/material.dart';
import 'package:medical/models/models.dart';

class MedicalReportViewerScreen extends StatelessWidget {
  final MedicalReport report;

  const MedicalReportViewerScreen({super.key, required this.report});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(report.reportName),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareReport(context),
          ),
        ],
      ),
      body: _buildReportViewer(),
    );
  }

  Widget _buildReportViewer() {
    final file = File(report.reportPath);
    
    if (!file.existsSync()) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'File not found',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'The medical report file could not be found.',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Check if it's an image file
    final extension = report.reportPath.toLowerCase().split('.').last;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return _buildImageViewer(file);
    } else {
      return _buildFileInfo(file);
    }
  }

  Widget _buildImageViewer(File file) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Image viewer
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.file(
                file,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.broken_image, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('Unable to load image'),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          
          // File information
          _buildFileInfoCard(file),
        ],
      ),
    );
  }

  Widget _buildFileInfo(File file) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // File icon
          const Icon(
            Icons.insert_drive_file,
            size: 64,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          
          // File name
          Text(
            report.reportName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          
          // File type info
          Text(
            'File type not supported for preview',
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          
          // File information card
          _buildFileInfoCard(file),
          
          const SizedBox(height: 24),
          
          // Open with external app button
          ElevatedButton.icon(
            onPressed: () => _openWithExternalApp(),
            icon: const Icon(Icons.open_in_new),
            label: const Text('Open with external app'),
          ),
        ],
      ),
    );
  }

  Widget _buildFileInfoCard(File file) {
    final fileStat = file.statSync();
    final fileSize = _formatFileSize(fileStat.size);
    final lastModified = _formatDate(fileStat.modified);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'File Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Name', report.reportName),
            _buildInfoRow('Size', fileSize),
            _buildInfoRow('Last Modified', lastModified),
            _buildInfoRow('Path', report.reportPath),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _shareReport(BuildContext context) {
    // TODO: Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality not implemented yet'),
      ),
    );
  }

  void _openWithExternalApp() {
    // TODO: Implement opening with external app
    // This would typically use url_launcher or similar package
  }
}
